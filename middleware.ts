import { NextRequest, NextResponse } from 'next/server';
import * as jose from 'jose';

const routeConfig = {
	public: ['/404', '/500', '/401', '/403'],

	auth: ['/signin', '/register'],

	protected: {
		'/': { roles: ['ADMIN', 'USER'], statuses: ['ACTIVE'] },
		'/projects': { roles: ['ADMIN', 'USER'], statuses: ['ACTIVE'] },
		'/generations': { roles: ['ADMIN', 'USER'], statuses: ['ACTIVE'] },
	},

	statusRoutes: {
		'/account-status': {
			roles: ['ADMIN', 'USER'],
			statuses: ['INACTIVE', 'REJECTED'],
		},
		'/unauthorized': {
			roles: ['ADMIN', 'USER'],
			statuses: ['ACTIVE', 'INACTIVE', 'REJECTED'],
		},
	},
};

const isPublicRoute = (pathname: string) =>
	routeConfig.public.some((route) => pathname.startsWith(route));

const isAuthRoute = (pathname: string) =>
	routeConfig.auth.some((route) => pathname.startsWith(route));

const getRouteConfig = (pathname: string) => {
	for (const [route, config] of Object.entries(routeConfig.protected)) {
		if (pathname === route || (route !== '/' && pathname.startsWith(route))) {
			return { type: 'protected', config };
		}
	}

	for (const [route, config] of Object.entries(routeConfig.statusRoutes)) {
		if (pathname === route || pathname.startsWith(route)) {
			return { type: 'status', config };
		}
	}

	return null;
};

export async function middleware(req: NextRequest) {
	const pathname = req.nextUrl.pathname;
	const url = req.nextUrl.clone();

	if (isPublicRoute(pathname)) {
		return NextResponse.next();
	}

	const isAuth = isAuthRoute(pathname);
	const routeInfo = getRouteConfig(pathname);

	const token = req.cookies.get('auth-token')?.value;

	if (routeInfo && !token) {
		url.pathname = '/signin';
		url.searchParams.set('redirect', pathname);
		return NextResponse.redirect(url);
	}

	if (token) {
		try {
			const secret = process.env.NEXT_PUBLIC_JWT_SECRET;
			if (!secret) {
				console.error('JWT_SECRET is not defined');
				if (routeInfo) {
					url.pathname = '/signin';
					return NextResponse.redirect(url);
				}
				return NextResponse.next();
			}

			const { payload } = await jose.jwtVerify(
				token,
				new TextEncoder().encode(secret),
			);

			const userRole = payload.role as 'ADMIN' | 'USER';
			const userStatus = payload.status as 'ACTIVE' | 'INACTIVE' | 'REJECTED';
			const tokenType = payload.type as string;

			if (tokenType !== 'auth') {
				url.pathname = '/signin';
				url.searchParams.set('error', 'invalid_token');
				const response = NextResponse.redirect(url);
				response.cookies.delete('auth-token');
				return response;
			}

			if (isAuth) {
				url.pathname = '/';
				return NextResponse.redirect(url);
			}

			if (routeInfo) {
				const { config } = routeInfo;

				if (!config.roles.includes(userRole)) {
					url.pathname = '/unauthorized';
					return NextResponse.redirect(url);
				}

				if (!config.statuses.includes(userStatus)) {
					if (userStatus === 'INACTIVE' || userStatus === 'REJECTED') {
						url.pathname = '/account-status';
						return NextResponse.redirect(url);
					}
					url.pathname = '/unauthorized';
					return NextResponse.redirect(url);
				}
			}

			const response = NextResponse.next();
			response.headers.set('x-user-email', payload.email as string);
			response.headers.set('x-user-role', userRole);
			response.headers.set('x-user-status', userStatus);

			return response;
		} catch (error) {
			if (routeInfo) {
				url.pathname = '/signin';
				url.searchParams.set('error', 'session_expired');
				const response = NextResponse.redirect(url);
				response.cookies.delete('auth-token');
				return response;
			}

			const response = NextResponse.next();
			response.cookies.delete('auth-token');
			return response;
		}
	}

	return NextResponse.next();
}

export const config = {
	matcher: [
		'/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
	],
};
