# Server-Sent Events (SSE) Integration Guide

## Overview

The backend now supports real-time streaming of generation events using Server-Sent Events (SSE). This provides live updates on generation progress without the need for polling.

## SSE Endpoint

```
GET /api/generations/{generationId}/events
```

**Authentication**: Bearer token required in Authorization header

## Event Types

The SSE stream sends JSON-formatted events with the following types:

### 1. Status Event
Sent immediately when connecting to provide current status:
```json
{
  "type": "status",
  "status": "PENDING|IN_PROGRESS|COMPLETED|FAILED",
  "generationId": "uuid",
  "name": "Generation Name",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 2. Progress Event
Sent during generation to indicate progress:
```json
{
  "type": "progress",
  "status": "IN_PROGRESS",
  "generationId": "uuid",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 3. Completed Event
Sent when generation is successfully completed:
```json
{
  "type": "completed",
  "result": "Generated code or documentation",
  "metadata": {
    "model": "meta-llama/CodeLlama-7b-Instruct-hf",
    "processingTime": 2500,
    "framework": "React",
    "styling": "Tailwind CSS"
  },
  "generationId": "uuid",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 4. Failed Event
Sent when generation fails:
```json
{
  "type": "failed",
  "error": "Generation failed",
  "generationId": "uuid",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Frontend Implementation

### Basic JavaScript/TypeScript Implementation

```typescript
class GenerationStreamer {
  private eventSource: EventSource | null = null;
  
  startStreaming(generationId: string, token: string) {
    // Close existing connection if any
    this.stopStreaming();
    
    // Create EventSource with authentication
    const url = `/api/generations/${generationId}/events`;
    this.eventSource = new EventSource(url, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    // Handle incoming messages
    this.eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        this.handleEvent(data);
      } catch (error) {
        console.error('Failed to parse SSE data:', error);
      }
    };
    
    // Handle connection errors
    this.eventSource.onerror = (error) => {
      console.error('SSE connection error:', error);
      this.handleError(error);
    };
    
    // Handle connection open
    this.eventSource.onopen = () => {
      console.log('SSE connection established');
    };
  }
  
  private handleEvent(data: any) {
    switch (data.type) {
      case 'status':
        this.onStatus(data);
        break;
      case 'progress':
        this.onProgress(data);
        break;
      case 'completed':
        this.onCompleted(data);
        this.stopStreaming(); // Close connection
        break;
      case 'failed':
        this.onFailed(data);
        this.stopStreaming(); // Close connection
        break;
      default:
        console.warn('Unknown event type:', data.type);
    }
  }
  
  private onStatus(data: any) {
    console.log('Generation status:', data.status);
    // Update UI with current status
  }
  
  private onProgress(data: any) {
    console.log('Generation in progress:', data.status);
    // Show loading indicator
  }
  
  private onCompleted(data: any) {
    console.log('Generation completed:', data.result);
    // Display the generated result
    // Hide loading indicator
  }
  
  private onFailed(data: any) {
    console.error('Generation failed:', data.error);
    // Show error message
    // Hide loading indicator
  }
  
  private handleError(error: any) {
    console.error('SSE error:', error);
    // Handle connection errors
    // Maybe retry connection or show error to user
  }
  
  stopStreaming() {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
  }
}
```

### React Hook Implementation

```typescript
import { useEffect, useRef, useState } from 'react';

interface GenerationEvent {
  type: 'status' | 'progress' | 'completed' | 'failed';
  status?: string;
  result?: string;
  error?: string;
  metadata?: any;
  generationId: string;
  timestamp: string;
}

export const useGenerationStream = (generationId: string, token: string) => {
  const [status, setStatus] = useState<string>('PENDING');
  const [result, setResult] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const eventSourceRef = useRef<EventSource | null>(null);
  
  useEffect(() => {
    if (!generationId || !token) return;
    
    const url = `/api/generations/${generationId}/events`;
    const eventSource = new EventSource(url, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    eventSourceRef.current = eventSource;
    
    eventSource.onopen = () => {
      setIsConnected(true);
      setError(null);
    };
    
    eventSource.onmessage = (event) => {
      try {
        const data: GenerationEvent = JSON.parse(event.data);
        
        switch (data.type) {
          case 'status':
          case 'progress':
            setStatus(data.status || 'UNKNOWN');
            break;
          case 'completed':
            setStatus('COMPLETED');
            setResult(data.result || null);
            eventSource.close();
            break;
          case 'failed':
            setStatus('FAILED');
            setError(data.error || 'Generation failed');
            eventSource.close();
            break;
        }
      } catch (err) {
        console.error('Failed to parse SSE data:', err);
      }
    };
    
    eventSource.onerror = (err) => {
      console.error('SSE connection error:', err);
      setIsConnected(false);
      setError('Connection error');
    };
    
    return () => {
      eventSource.close();
      setIsConnected(false);
    };
  }, [generationId, token]);
  
  const disconnect = () => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      setIsConnected(false);
    }
  };
  
  return {
    status,
    result,
    error,
    isConnected,
    disconnect
  };
};
```

### Usage Example

```typescript
// In your React component
const MyComponent = () => {
  const [generationId, setGenerationId] = useState<string>('');
  const [token] = useState<string>('your-jwt-token');
  
  const { status, result, error, isConnected } = useGenerationStream(generationId, token);
  
  const startGeneration = async () => {
    // Create a new generation
    const response = await fetch('/api/generations/conversational', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        name: "My Component",
        type: "UI",
        initialPrompt: "Create a button component",
        projectId: "your-project-id"
      })
    });
    
    const { data } = await response.json();
    setGenerationId(data.id); // This will trigger the SSE connection
  };
  
  return (
    <div>
      <button onClick={startGeneration}>Start Generation</button>
      
      {isConnected && <div>Connected to generation stream</div>}
      
      <div>Status: {status}</div>
      
      {status === 'IN_PROGRESS' && <div>Generating...</div>}
      
      {result && (
        <div>
          <h3>Generated Result:</h3>
          <pre>{result}</pre>
        </div>
      )}
      
      {error && <div style={{color: 'red'}}>Error: {error}</div>}
    </div>
  );
};
```

## Important Notes

1. **Authentication**: The SSE endpoint requires a valid JWT token in the Authorization header
2. **Connection Management**: Always close EventSource connections when components unmount
3. **Error Handling**: Implement proper error handling for connection failures
4. **Automatic Cleanup**: The server automatically closes the connection when generation completes or fails
5. **Polling Fallback**: The SSE implementation uses internal polling (1-second intervals) to check for updates

## Browser Compatibility

Server-Sent Events are supported in all modern browsers. For older browsers, consider using a polyfill or fallback to regular polling.

## Testing

You can test the SSE endpoint using curl:

```bash
curl -N -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:3000/api/generations/YOUR_GENERATION_ID/events
```

This will show the real-time events as they're sent from the server.
