import { useState, useEffect, useCallback } from 'react';
import {
	GenerationAPI,
	GenerationResponse,
	CreateGenerationRequest,
	AddMessageRequest,
	ConversationMessage,
	ConversationHistoryResponse,
} from '@/api/GenerationApi';

// Extended interface for generation list with project and user info
export interface GenerationListItem extends GenerationResponse {
	project: {
		id: string;
		name: string;
		description?: string;
		status: 'ACTIVE' | 'INACTIVE' | 'ARCHIVED';
	};
	createdBy: {
		id: string;
		email: string;
		fullName?: string;
	};
	promptHistory?: {
		id: string;
		prompt: string;
		description?: string;
		tags: string[];
		usageCount: number;
	};
}

const generationApi = new GenerationAPI();

export const useGenerations = () => {
	const [generations, setGenerations] = useState<GenerationListItem[]>([]);
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const fetchGenerations = useCallback(
		async (params?: {
			page?: number;
			limit?: number;
			type?: 'UI' | 'DOCUMENTATION';
			status?: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
		}) => {
			setIsLoading(true);
			setError(null);
			try {
				const response = await generationApi.getUserGenerations(params);
				if (response.status === 200) {
					setGenerations(
						(response.data as { data: GenerationListItem[] }).data || [],
					);
				} else {
					setError(
						(response.data as { message: string }).message ||
							'Failed to fetch generations',
					);
				}
			} catch (err) {
				setError(err instanceof Error ? err.message : 'An error occurred');
			} finally {
				setIsLoading(false);
			}
		},
		[],
	);

	const createGeneration = useCallback(
		async (data: CreateGenerationRequest) => {
			setIsLoading(true);
			setError(null);
			try {
				// Use the general endpoint from the service
				const response = await generationApi.createConversationalGeneration(
					data,
				);
				if (response.status === 201) {
					// Add the new generation to the list
					const newGeneration = (response.data as { data: GenerationListItem })
						.data;
					setGenerations((prev) => [newGeneration, ...prev]);
					return newGeneration;
				} else {
					setError(
						(response.data as { message: string }).message ||
							'Failed to create generation',
					);
					return null;
				}
			} catch (err) {
				setError(err instanceof Error ? err.message : 'An error occurred');
				return null;
			} finally {
				setIsLoading(false);
			}
		},
		[],
	);

	const createProjectGeneration = useCallback(
		async (
			projectId: string,
			data: Omit<CreateGenerationRequest, 'projectId'>,
		) => {
			setIsLoading(true);
			setError(null);
			try {
				// Use the project-specific endpoint
				const response =
					await generationApi.createProjectConversationalGeneration(
						projectId,
						data,
					);
				if (response.status === 201) {
					// Add the new generation to the list
					const newGeneration = (response.data as { data: GenerationListItem })
						.data;
					setGenerations((prev) => [newGeneration, ...prev]);
					return newGeneration;
				} else {
					setError(
						(response.data as { message: string }).message ||
							'Failed to create generation',
					);
					return null;
				}
			} catch (err) {
				setError(err instanceof Error ? err.message : 'An error occurred');
				return null;
			} finally {
				setIsLoading(false);
			}
		},
		[],
	);

	const deleteGeneration = useCallback(async (generationId: string) => {
		setIsLoading(true);
		setError(null);
		try {
			const response = await generationApi.deleteGeneration(generationId);
			if (response.status === 200) {
				setGenerations((prev) => prev.filter((gen) => gen.id !== generationId));
				return true;
			} else {
				setError(
					(response.data as { message: string }).message ||
						'Failed to delete generation',
				);
				return false;
			}
		} catch (err) {
			setError(err instanceof Error ? err.message : 'An error occurred');
			return false;
		} finally {
			setIsLoading(false);
		}
	}, []);

	return {
		generations,
		isLoading,
		error,
		fetchGenerations,
		createGeneration,
		createProjectGeneration,
		deleteGeneration,
	};
};

export const useGeneration = (generationId: string) => {
	const [generation, setGeneration] = useState<GenerationListItem | null>(null);
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const fetchGeneration = useCallback(async () => {
		if (!generationId) return;

		setIsLoading(true);
		setError(null);
		try {
			const response = await generationApi.getGeneration(generationId);
			if (response.status === 200) {
				setGeneration((response.data as { data: GenerationListItem }).data);
			} else {
				setError(
					(response.data as { message: string }).message ||
						'Failed to fetch generation',
				);
			}
		} catch (err) {
			setError(err instanceof Error ? err.message : 'An error occurred');
		} finally {
			setIsLoading(false);
		}
	}, [generationId]);

	const getResult = useCallback(async () => {
		if (!generationId) return null;

		try {
			const response = await generationApi.getGenerationResult(generationId);
			if (response.status === 200) {
				return (response.data as { data: unknown }).data;
			} else {
				setError(
					(response.data as { message: string }).message ||
						'Failed to fetch generation result',
				);
				return null;
			}
		} catch (err) {
			setError(err instanceof Error ? err.message : 'An error occurred');
			return null;
		}
	}, [generationId]);

	const updateGeneration = useCallback(
		async (data: { name?: string; metadata?: Record<string, unknown> }) => {
			if (!generationId) return false;

			setIsLoading(true);
			setError(null);
			try {
				const response = await generationApi.updateGeneration(
					generationId,
					data,
				);
				if (response.status === 200) {
					setGeneration((response.data as { data: GenerationListItem }).data);
					return true;
				} else {
					setError(
						(response.data as { message: string }).message ||
							'Failed to update generation',
					);
					return false;
				}
			} catch (err) {
				setError(err instanceof Error ? err.message : 'An error occurred');
				return false;
			} finally {
				setIsLoading(false);
			}
		},
		[generationId],
	);

	useEffect(() => {
		fetchGeneration();
	}, [fetchGeneration]);

	return {
		generation,
		isLoading,
		error,
		fetchGeneration,
		getResult,
		updateGeneration,
	};
};

export const useConversation = (generationId: string) => {
	const [messages, setMessages] = useState<ConversationMessage[]>([]);
	const [pagination, setPagination] = useState({
		page: 1,
		limit: 50,
		total: 0,
		totalPages: 0,
	});
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const fetchConversation = useCallback(
		async (params?: { page?: number; limit?: number }) => {
			if (!generationId) return;

			setIsLoading(true);
			setError(null);
			try {
				const response = await generationApi.getConversationHistory(
					generationId,
					params,
				);
				if (response.status === 200) {
					const data: ConversationHistoryResponse = (
						response.data as { data: ConversationHistoryResponse }
					).data;
					setMessages(data.messages);
					setPagination(data.pagination);
				} else {
					setError(
						(response.data as { message: string }).message ||
							'Failed to fetch conversation',
					);
				}
			} catch (err) {
				setError(err instanceof Error ? err.message : 'An error occurred');
			} finally {
				setIsLoading(false);
			}
		},
		[generationId],
	);

	const addMessage = useCallback(
		async (data: AddMessageRequest) => {
			if (!generationId) return null;

			setIsLoading(true);
			setError(null);
			try {
				const response = await generationApi.addMessage(generationId, data);
				if (response.status === 201) {
					const newMessage = (response.data as { data: ConversationMessage })
						.data;
					setMessages((prev) => [...prev, newMessage]);
					return newMessage;
				} else {
					setError(
						(response.data as { message: string }).message ||
							'Failed to add message',
					);
					return null;
				}
			} catch (err) {
				setError(err instanceof Error ? err.message : 'An error occurred');
				return null;
			} finally {
				setIsLoading(false);
			}
		},
		[generationId],
	);

	const loadMore = useCallback(async () => {
		if (pagination.page >= pagination.totalPages) return;

		const nextPage = pagination.page + 1;
		setIsLoading(true);
		setError(null);
		try {
			const response = await generationApi.getConversationHistory(
				generationId,
				{
					page: nextPage,
					limit: pagination.limit,
				},
			);
			if (response.status === 200) {
				const data: ConversationHistoryResponse = (
					response.data as { data: ConversationHistoryResponse }
				).data;
				setMessages((prev) => [...prev, ...data.messages]);
				setPagination(data.pagination);
			} else {
				setError(
					(response.data as { message: string }).message ||
						'Failed to load more messages',
				);
			}
		} catch (err) {
			setError(err instanceof Error ? err.message : 'An error occurred');
		} finally {
			setIsLoading(false);
		}
	}, [generationId, pagination]);

	useEffect(() => {
		fetchConversation();
	}, [fetchConversation]);

	return {
		messages,
		pagination,
		isLoading,
		error,
		fetchConversation,
		addMessage,
		loadMore,
	};
};

export const useGenerationStream = (generationId: string) => {
	const [isConnected, setIsConnected] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [lastUpdate, setLastUpdate] = useState<any>(null);

	useEffect(() => {
		if (!generationId) return;

		const eventSource = generationApi.createGenerationStream(generationId);

		eventSource.onopen = () => {
			setIsConnected(true);
			setError(null);
			console.log('Generation stream connected');
		};

		eventSource.onmessage = (event) => {
			try {
				const data = JSON.parse(event.data);
				setLastUpdate(data);
				console.log('Generation stream update:', data);
			} catch (err) {
				console.error('Failed to parse stream data:', err);
			}
		};

		eventSource.onerror = (event) => {
			console.error('Generation stream error:', event);
			setIsConnected(false);
			setError('Stream connection failed');
		};

		return () => {
			eventSource.close();
			setIsConnected(false);
		};
	}, [generationId]);

	return {
		isConnected,
		error,
		lastUpdate,
	};
};
