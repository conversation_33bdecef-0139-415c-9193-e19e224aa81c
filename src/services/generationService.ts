import {
	GenerationAPI,
	CreateGenerationRequest,
	AddMessageRequest,
	GenerationResponse,
	ConversationMessage,
} from '@/api/GenerationApi';

const generationApi = new GenerationAPI();

export const generationService = {
	/**
	 * Create a new UI generation
	 */
	createUIGeneration: async (
		projectId: string,
		prompt: string,
		name?: string,
	) => {
		const data: CreateGenerationRequest = {
			name: name || 'UI Generation',
			type: 'UI',
			initialPrompt: prompt,
			projectId,
		};

		return await generationApi.createConversationalGeneration(data);
	},

	/**
	 * Create a new documentation generation
	 */
	createDocumentationGeneration: async (
		projectId: string,
		prompt: string,
		name?: string,
	) => {
		const data: CreateGenerationRequest = {
			name: name || 'Documentation Generation',
			type: 'DOCUMENTATION',
			initialPrompt: prompt,
			projectId,
		};

		return await generationApi.createConversationalGeneration(data);
	},

	/**
	 * Continue a conversation with a follow-up message
	 */
	continueConversation: async (
		generationId: string,
		message: string,
		inputData?: Record<string, unknown>,
	) => {
		const data: AddMessageRequest = {
			content: message,
			inputData,
		};

		return await generationApi.addMessage(generationId, data);
	},

	/**
	 * Get the current generated code/documentation
	 */
	getCurrentResult: async (generationId: string) => {
		const response = await generationApi.getGenerationResult(generationId);
		if (response.status === 200) {
			return (response.data as { data: { currentResult: string } }).data
				.currentResult;
		}
		return null;
	},

	/**
	 * Get conversation history with pagination
	 */
	getConversationHistory: async (
		generationId: string,
		page = 1,
		limit = 20,
	) => {
		const response = await generationApi.getConversationHistory(generationId, {
			page,
			limit,
		});
		if (response.status === 200) {
			return (response.data as { data: ConversationMessage[] }).data;
		}
		return null;
	},

	/**
	 * Check if generation is still processing
	 */
	isProcessing: (status: string) => {
		return status === 'PENDING' || status === 'IN_PROGRESS';
	},

	/**
	 * Get status badge info for UI display
	 */
	getStatusBadge: (status: string) => {
		switch (status) {
			case 'COMPLETED':
				return { variant: 'success' as const, text: 'Completed' };
			case 'IN_PROGRESS':
				return { variant: 'warning' as const, text: 'In Progress' };
			case 'PENDING':
				return { variant: 'secondary' as const, text: 'Pending' };
			case 'FAILED':
				return { variant: 'error' as const, text: 'Failed' };
			default:
				return { variant: 'secondary' as const, text: 'Unknown' };
		}
	},

	/**
	 * Format generation type for display
	 */
	formatType: (type: string) => {
		switch (type) {
			case 'UI':
				return 'UI Component';
			case 'DOCUMENTATION':
				return 'Documentation';
			default:
				return type;
		}
	},

	/**
	 * Poll for generation updates
	 */
	pollForUpdates: (
		generationId: string,
		callback: (generation: GenerationResponse) => void,
		interval = 2000,
	) => {
		const pollInterval = setInterval(async () => {
			try {
				const response = await generationApi.getGeneration(generationId);
				if (response.status === 200) {
					const generation = (response.data as { data: GenerationResponse })
						.data;
					callback(generation);

					// Stop polling if generation is completed or failed
					if (
						generation.status === 'COMPLETED' ||
						generation.status === 'FAILED'
					) {
						clearInterval(pollInterval);
					}
				}
			} catch (error) {
				console.error('Error polling for generation updates:', error);
				clearInterval(pollInterval);
			}
		}, interval);

		return pollInterval;
	},
};
