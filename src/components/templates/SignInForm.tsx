import React, { useState, useEffect } from 'react';
import { Field, Label, Fieldset } from '@headlessui/react';
import {
	Input,
	<PERSON>ton,
	<PERSON>ert,
	ButtonLoader,
	Typography,
} from '@/components/atoms';
import { useRouter } from 'next/router';
import { useAuth } from '@/hooks/useAuth';

export default function SignInForm() {
	const [message, setMessage] = useState<string | null>(null);
	const router = useRouter();
	const { isLoading, error, clearError, login } = useAuth();

	useEffect(() => {
		const urlMessage = router.query.message as string;
		if (urlMessage) {
			setMessage(urlMessage);
		}
	}, [router.query.message]);

	const onSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
		event.preventDefault();
		setMessage(null);
		clearError();

		const formData = new FormData(event.currentTarget);
		const email = formData.get('email') as string;
		const password = formData.get('password') as string;

		if (!email || !password) {
			return;
		}

		await login({ email, password });
	};
	return (
		<div className='w-full'>
			<div className='text-center mb-4'>
				<Typography
					variant='h2'
					responsive
					className='mb-1'>
					Sign in to your account
				</Typography>
				<Typography
					variant='body'
					color='secondary'>
					Welcome back! Please enter your details.
				</Typography>
			</div>

			{message && (
				<Alert
					className='mb-3'
					variant='default'>
					{message}
				</Alert>
			)}
			{error && (
				<Alert
					className='mb-3'
					variant='negative'>
					{error}
				</Alert>
			)}

			<form
				onSubmit={onSubmit}
				className='space-y-3 sm:space-y-4'>
				<Fieldset className='space-y-3'>
					<Field>
						<Label>
							<Typography
								variant='body-sm'
								weight='medium'
								color='secondary'
								className='mb-1'>
								Email address
							</Typography>
						</Label>
						<Input
							id='email'
							name='email'
							type='email'
							required
							autoComplete='email'
							placeholder='<EMAIL>'
							className='w-full'
							inputMode='email'
						/>
					</Field>

					<Field>
						<Label>
							<Typography
								variant='body-sm'
								weight='medium'
								color='secondary'
								className='mb-1'>
								Password
							</Typography>
						</Label>
						<Input
							id='password'
							name='password'
							type='password'
							required
							autoComplete='current-password'
							placeholder='●●●●●●●●'
							className='w-full'
						/>
					</Field>
				</Fieldset>

				<Button
					variant='primary'
					size='lg'
					disabled={isLoading}
					type='submit'
					className='w-full mt-4'>
					{isLoading && <ButtonLoader />}
					{isLoading ? 'Signing in...' : 'Sign in'}
				</Button>
			</form>
		</div>
	);
}
