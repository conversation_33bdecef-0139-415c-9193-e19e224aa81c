import { useAuthStore } from '@/providers/auth-store-provider';
import Menubar from '../organisms/Menubar';
import { useEffect } from 'react';
import { useRouter } from 'next/router';

export default function AppTemplate({
	children,
}: {
	children: React.ReactNode;
}) {
	const router = useRouter();
	const links = [
		{
			title: 'Home',
			href: '/',
			startIcon: null,
			endIcon: null,
			scope: 'user',
			onClick: () => {},
			className: '',
			disabled: false,
		},
		{
			title: 'Projects',
			href: '/projects',
			startIcon: null,
			endIcon: null,
			scope: 'user',
			onClick: () => {},
			className: '',
			disabled: false,
		},
	];
	const authState = useAuthStore((state) => state);
	useEffect(() => {
		if (!authState.user) {
			router.push('/signin');
		}
	}, []);
	return (
		<div className='flex min-h-screen flex-col bg-base text-foreground'>
			<Menubar
				logo='PixiGenerator'
				links={links}
			/>
			<main className='flex-1'>{children}</main>
		</div>
	);
}
