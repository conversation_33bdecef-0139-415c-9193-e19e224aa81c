import { useState, useEffect } from 'react';
import { Button } from '@/components/atoms';
import {
	ChevronLeftIcon,
	ChevronRightIcon,
	ChatBubbleLeftRightIcon,
	EyeIcon,
} from '@heroicons/react/24/outline';

interface GenerationsLayoutProps {
	chatPanel: React.ReactNode;
	codePreviewPanel: React.ReactNode;
}

export const GenerationsLayout: React.FC<GenerationsLayoutProps> = ({
	chatPanel,
	codePreviewPanel,
}) => {
	const [isChatPanelCollapsed, setIsChatPanelCollapsed] = useState(false);
	const [isMobile, setIsMobile] = useState(false);
	const [mobileView, setMobileView] = useState<'chat' | 'preview'>('chat');

	useEffect(() => {
		const mediaQuery = window.matchMedia('(max-width: 768px)');
		const handleResize = (e: MediaQueryListEvent | { matches: boolean }) => {
			setIsMobile(e.matches);
			if (e.matches) {
				setIsChatPanelCollapsed(true);
			} else {
				setIsChatPanelCollapsed(false);
			}
		};

		// Initial check
		handleResize({ matches: mediaQuery.matches });

		// Use addEventListener for modern browsers
		mediaQuery.addEventListener('change', handleResize);

		return () => {
			mediaQuery.removeEventListener('change', handleResize);
		};
	}, []);

	const toggleChatPanel = () => {
		setIsChatPanelCollapsed(!isChatPanelCollapsed);
	};

	if (isMobile) {
		return (
			<div className='flex flex-col h-full'>
				<div className='flex-shrink-0 border-b border-border-secondary p-2 flex justify-center gap-2 bg-surface-50'>
					<Button
						variant={mobileView === 'chat' ? 'primary' : 'ghost'}
						onClick={() => setMobileView('chat')}
						size='sm'
						className='flex-1 justify-center'
						aria-label='Chat'>
						<ChatBubbleLeftRightIcon className='w-5 h-5 sm:mr-2' />
						<span className='hidden sm:inline'>Chat</span>
					</Button>
					<Button
						variant={mobileView === 'preview' ? 'primary' : 'ghost'}
						onClick={() => setMobileView('preview')}
						size='sm'
						className='flex-1 justify-center'
						aria-label='Preview'>
						<EyeIcon className='w-5 h-5 sm:mr-2' />
						<span className='hidden sm:inline'>Preview</span>
					</Button>
				</div>
				<div className='flex-1 overflow-auto'>
					{mobileView === 'chat' && chatPanel}
					{mobileView === 'preview' && codePreviewPanel}
				</div>
			</div>
		);
	}

	return (
		<div className='flex h-full'>
			{/* Chat Panel */}
			<div
				className={`
          flex-shrink-0 bg-surface-50 border-r border-border-secondary transition-all duration-300 ease-in-out relative
          ${isChatPanelCollapsed ? 'w-0' : 'w-80 lg:w-96'}
        `}>
				<div
					className={`h-full overflow-hidden ${
						isChatPanelCollapsed ? 'hidden' : 'block'
					}`}>
					{chatPanel}
				</div>
			</div>

			{/* Main Content: Code & Preview */}
			<div className='flex-1 flex flex-col bg-surface-100 relative'>
				<Button
					variant='ghost'
					size='sm'
					onClick={toggleChatPanel}
					className='absolute top-1/2 -translate-y-1/2 bg-surface-50 border border-border-secondary rounded-full p-1.5 z-10 transition-all duration-300 -ml-4 hover:bg-surface-100'
					style={{ left: isChatPanelCollapsed ? '0.5rem' : '0' }}
					aria-label={
						isChatPanelCollapsed ? 'Expand chat panel' : 'Collapse chat panel'
					}>
					{isChatPanelCollapsed ? (
						<ChevronRightIcon className='w-4 h-4' />
					) : (
						<ChevronLeftIcon className='w-4 h-4' />
					)}
				</Button>
				{codePreviewPanel}
			</div>
		</div>
	);
};
