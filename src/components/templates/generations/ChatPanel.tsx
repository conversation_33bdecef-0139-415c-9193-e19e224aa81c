import React, { useState, useEffect, useRef } from 'react';
import {
	SparklesIcon,
	UserIcon,
	ArrowPathIcon,
	TagIcon,
} from '@heroicons/react/24/outline';
import { Typography } from '@/components/atoms';
import { PromptInput } from '@/components/molecules';
import { Button } from '@/components/atoms';
import { Badge } from '@/components/atoms';
import { GenerationResponse, ConversationMessage } from '@/api/GenerationApi';

interface ChatPanelProps {
	generation: GenerationResponse;
	messages: ConversationMessage[];
	onSendMessage: (message: string) => Promise<void>;
	onRegenerateCode: (prompt: string) => void;
	isGenerating: boolean;
	streamStatus?: string;
	streamResult?: string | null;
	isStreamConnected?: boolean;
}

export const ChatPanel: React.FC<ChatPanelProps> = ({
	generation,
	messages,
	onSendMessage,
	onRegenerateCode,
	isGenerating,
	streamStatus = 'PENDING',
	streamResult,
	isStreamConnected = false,
}) => {
	const [newMessage, setNewMessage] = useState('');
	const messagesEndRef = useRef<HTMLDivElement>(null);

	const scrollToBottom = () => {
		messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
	};

	useEffect(() => {
		scrollToBottom();
	}, [messages]);

	const handleSendMessage = async (message: string) => {
		if (!message.trim() || isGenerating) return;

		setNewMessage('');
		try {
			await onSendMessage(message);
		} catch (error) {
			console.error('Error sending message:', error);
		}
	};

	const formatTime = (date: Date) => {
		return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
	};

	return (
		<div className='flex flex-col h-full bg-surface-50'>
			{/* Header */}
			<div className='p-4 border-b border-border-secondary bg-surface-100'>
				<div className='flex items-center gap-3'>
					<div className='p-2 bg-primary/10 rounded-lg'>
						<SparklesIcon className='w-5 h-5 text-primary' />
					</div>
					<div>
						<Typography
							variant='h4'
							weight='semibold'>
							{generation.type === 'UI' ? 'UI Generation' : 'Documentation'}
						</Typography>
						<Typography
							variant='body-sm'
							color='secondary'>
							{generation.project?.name}
						</Typography>
					</div>
				</div>

				{/* Generation tags */}
				{generation.promptHistory &&
					generation.promptHistory.tags.length > 0 && (
						<div className='flex flex-wrap gap-1 mt-3'>
							{generation.promptHistory.tags.map(
								(tag: string, index: number) => (
									<Badge
										key={index}
										variant='secondary'
										size='small'
										className='text-xs'>
										<TagIcon className='w-3 h-3 mr-1' />
										{tag}
									</Badge>
								),
							)}
						</div>
					)}
			</div>

			{/* Messages */}
			<div className='flex-1 overflow-y-auto p-4 space-y-4'>
				{messages.map((message) => (
					<div
						key={message.id}
						className={`flex ${
							message.role === 'USER' ? 'justify-end' : 'justify-start'
						}`}>
						{message.role === 'ASSISTANT' && (
							<div className='flex-shrink-0 w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center mr-3'>
								<SparklesIcon className='w-4 h-4 text-primary' />
							</div>
						)}
						<div
							className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
								message.role === 'USER'
									? 'bg-primary text-white'
									: 'bg-surface-100 text-foreground'
							}`}>
							<Typography
								variant='body-sm'
								className={`whitespace-pre-wrap ${
									message.role === 'USER' ? 'text-white' : ''
								}`}>
								{message.content}
							</Typography>
							<Typography
								variant='caption'
								className={`block mt-1 text-xs ${
									message.role === 'USER' ? 'text-white/70' : 'text-muted'
								}`}>
								{formatTime(new Date(message.createdAt))}
							</Typography>
						</div>
						{message.role === 'USER' && (
							<div className='flex-shrink-0 w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center ml-3'>
								<UserIcon className='w-4 h-4 text-primary' />
							</div>
						)}
					</div>
				))}

				{isGenerating && (
					<div className='flex justify-start'>
						<div className='flex-shrink-0 w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center mr-3'>
							<SparklesIcon className='w-4 h-4 text-primary' />
						</div>
						<div className='max-w-xs lg:max-w-md px-4 py-2 rounded-lg bg-surface-100 text-foreground'>
							<div className='flex items-center gap-2'>
								<div className='w-2 h-2 bg-primary rounded-full animate-bounce' />
								<div
									className='w-2 h-2 bg-primary rounded-full animate-bounce'
									style={{ animationDelay: '0.1s' }}
								/>
								<div
									className='w-2 h-2 bg-primary rounded-full animate-bounce'
									style={{ animationDelay: '0.2s' }}
								/>
								<Typography
									variant='body-sm'
									color='secondary'>
									Generating response...
								</Typography>
							</div>
						</div>
					</div>
				)}

				<div ref={messagesEndRef} />
			</div>

			{/* Input */}
			<div className='p-4 border-t border-border-secondary bg-surface-100'>
				<div className='flex gap-2'>
					<PromptInput
						value={newMessage}
						onChange={setNewMessage}
						onSubmit={() => handleSendMessage(newMessage)}
						placeholder='Continue the conversation...'
						disabled={isGenerating}
						className='flex-1'
					/>
					<Button
						variant='secondary'
						size='sm'
						onClick={() => setNewMessage('')}
						disabled={!newMessage.trim() || isGenerating}>
						Clear
					</Button>
				</div>

				{/* Quick actions */}
				<div className='flex gap-2 mt-3'>
					<Button
						variant='ghost'
						size='sm'
						onClick={() => setNewMessage('Can you explain this code?')}
						disabled={isGenerating}>
						Explain code
					</Button>
					<Button
						variant='ghost'
						size='sm'
						onClick={() => setNewMessage('Make it more responsive')}
						disabled={isGenerating}>
						Improve responsiveness
					</Button>
					<Button
						variant='ghost'
						size='sm'
						onClick={() => onRegenerateCode(generation.prompt)}
						disabled={isGenerating}>
						<ArrowPathIcon className='w-4 h-4 mr-2' />
						Regenerate
					</Button>
				</div>
			</div>
		</div>
	);
};
