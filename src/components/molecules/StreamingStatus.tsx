import React from 'react';
import { Typo<PERSON>, Badge } from '@/components/atoms';
import { 
	WifiIcon, 
	ExclamationTriangleIcon,
	CheckCircleIcon,
	XCircleIcon,
	ClockIcon
} from '@heroicons/react/24/outline';

interface StreamingStatusProps {
	isConnected: boolean;
	streamStatus: string;
	streamError: string | null;
	lastEventTime?: string;
	className?: string;
}

export const StreamingStatus: React.FC<StreamingStatusProps> = ({
	isConnected,
	streamStatus,
	streamError,
	lastEventTime,
	className = '',
}) => {
	const getStatusInfo = () => {
		if (streamError) {
			return {
				icon: <ExclamationTriangleIcon className="w-4 h-4" />,
				text: 'Stream Error',
				variant: 'error' as const,
				description: streamError,
			};
		}

		if (!isConnected) {
			return {
				icon: <XCircleIcon className="w-4 h-4" />,
				text: 'Disconnected',
				variant: 'secondary' as const,
				description: 'Using polling fallback',
			};
		}

		switch (streamStatus) {
			case 'PENDING':
				return {
					icon: <ClockIcon className="w-4 h-4" />,
					text: 'Pending',
					variant: 'secondary' as const,
					description: 'Waiting to start',
				};
			case 'IN_PROGRESS':
				return {
					icon: <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin" />,
					text: 'Generating',
					variant: 'warning' as const,
					description: 'AI is working...',
				};
			case 'COMPLETED':
				return {
					icon: <CheckCircleIcon className="w-4 h-4" />,
					text: 'Completed',
					variant: 'success' as const,
					description: 'Generation finished',
				};
			case 'FAILED':
				return {
					icon: <XCircleIcon className="w-4 h-4" />,
					text: 'Failed',
					variant: 'error' as const,
					description: 'Generation failed',
				};
			default:
				return {
					icon: <WifiIcon className="w-4 h-4" />,
					text: 'Connected',
					variant: 'success' as const,
					description: 'Real-time updates active',
				};
		}
	};

	const statusInfo = getStatusInfo();

	return (
		<div className={`flex items-center gap-2 ${className}`}>
			<div className="flex items-center gap-1.5">
				<div className={`
					${statusInfo.variant === 'success' ? 'text-success' : ''}
					${statusInfo.variant === 'warning' ? 'text-warning' : ''}
					${statusInfo.variant === 'error' ? 'text-error' : ''}
					${statusInfo.variant === 'secondary' ? 'text-muted' : ''}
				`}>
					{statusInfo.icon}
				</div>
				<Badge
					variant={statusInfo.variant}
					size="small"
					emphasis="light">
					{statusInfo.text}
				</Badge>
			</div>
			
			{isConnected && (
				<div className="flex items-center gap-1">
					<div className="w-2 h-2 bg-success rounded-full animate-pulse" />
					<Typography
						variant="caption"
						color="tertiary">
						Live
					</Typography>
				</div>
			)}
			
			{lastEventTime && (
				<Typography
					variant="caption"
					color="tertiary"
					className="hidden sm:block">
					Last update: {new Date(lastEventTime).toLocaleTimeString()}
				</Typography>
			)}
		</div>
	);
};
