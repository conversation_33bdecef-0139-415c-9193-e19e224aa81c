import { CommonFunction } from '@/common/Function/Function';
import { Api } from '@/common/StandardApi';
import { ApiURL } from '@/config/config';

export interface CreateGenerationRequest {
	name: string;
	type: 'UI' | 'DOCUMENTATION';
	initialPrompt: string;
	projectId: string;
	promptHistoryId?: string;
}

export interface AddMessageRequest {
	content: string;
	inputData?: Record<string, unknown>;
}

export interface GenerationResponse {
	id: string;
	name: string;
	type: 'UI' | 'DOCUMENTATION';
	prompt: string;
	initialPrompt: string;
	status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
	result: string | null;
	currentResult: string | null;
	metadata: {
		model?: string;
		processingTime?: number;
		tokensGenerated?: number;
		framework?: string;
		styling?: string;
		language?: string;
		error?: boolean;
		errorMessage?: string;
	} | null;
	projectId: string;
	createdById: string;
	createdAt: string;
	updatedAt: string;
	// These will be populated by the API with expanded data
	project?: {
		id: string;
		name: string;
		description?: string;
		status?: 'ACTIVE' | 'INACTIVE' | 'ARCHIVED';
	};
	createdBy?: {
		id: string;
		email: string;
		fullName?: string;
	};
	promptHistory?: {
		id: string;
		prompt: string;
		description?: string;
		tags: string[];
		usageCount: number;
	};
}

export interface ConversationMessage {
	id: string;
	generationId: string;
	role: 'USER' | 'ASSISTANT' | 'SYSTEM';
	content: string;
	inputData: Record<string, unknown> | null;
	outputData: Record<string, unknown> | null;
	messageIndex: number;
	status: 'SENT' | 'DELIVERED' | 'FAILED';
	createdAt: string;
	updatedAt: string;
	createdBy?: {
		id: string;
		email: string;
		name: string;
	};
}

export interface ConversationHistoryResponse {
	messages: ConversationMessage[];
	pagination: {
		page: number;
		limit: number;
		total: number;
		totalPages: number;
	};
}

class GenerationAPI {
	_api: Api;

	constructor() {
		this._api = new Api(ApiURL);
	}

	/**
	 * Create a new conversational generation
	 */
	async createConversationalGeneration(body: CreateGenerationRequest) {
		return await this._api.post(
			`generations/conversational`,
			JSON.stringify(body),
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}

	/**
	 * Create a conversational generation for a specific project
	 */
	async createProjectConversationalGeneration(
		projectId: string,
		body: Omit<CreateGenerationRequest, 'projectId'>,
	) {
		return await this._api.post(
			`projects/${projectId}/generations/conversational`,
			JSON.stringify(body),
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}

	/**
	 * Create a legacy generation for a project (backward compatibility)
	 */
	async createLegacyGeneration(
		projectId: string,
		body: {
			type: 'UI' | 'DOCUMENTATION';
			prompt: string;
			promptHistoryId?: string;
		},
	) {
		return await this._api.post(
			`projects/${projectId}/generations`,
			JSON.stringify(body),
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}

	/**
	 * Add a message to an existing conversation
	 */
	async addMessage(generationId: string, body: AddMessageRequest) {
		return await this._api.post(
			`generations/${generationId}/messages`,
			JSON.stringify(body),
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}

	/**
	 * Get conversation history for a generation
	 */
	async getConversationHistory(
		generationId: string,
		params: {
			page?: number;
			limit?: number;
		} = {},
	) {
		const searchParams = new URLSearchParams();
		if (params.page) searchParams.append('page', params.page.toString());
		if (params.limit) searchParams.append('limit', params.limit.toString());

		const queryString = searchParams.toString();
		const endpoint = `generations/${generationId}/conversation${
			queryString ? `?${queryString}` : ''
		}`;

		return await this._api.get(
			endpoint,
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}

	/**
	 * Get the current result of a generation
	 */
	async getGenerationResult(generationId: string) {
		return await this._api.get(
			`generations/${generationId}/result`,
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}

	/**
	 * Get a specific generation by ID
	 */
	async getGeneration(generationId: string) {
		return await this._api.get(
			`generations/${generationId}`,
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}

	/**
	 * Get all generations for a project
	 */
	async getProjectGenerations(
		projectId: string,
		params: {
			page?: number;
			limit?: number;
			type?: 'UI' | 'DOCUMENTATION';
			status?: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
		} = {},
	) {
		const searchParams = new URLSearchParams();
		if (params.page) searchParams.append('page', params.page.toString());
		if (params.limit) searchParams.append('limit', params.limit.toString());
		if (params.type) searchParams.append('type', params.type);
		if (params.status) searchParams.append('status', params.status);

		const queryString = searchParams.toString();
		const endpoint = `projects/${projectId}/generations${
			queryString ? `?${queryString}` : ''
		}`;

		return await this._api.get(
			endpoint,
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}

	/**
	 * Get all generations for the current user
	 */
	async getUserGenerations(
		params: {
			page?: number;
			limit?: number;
			type?: 'UI' | 'DOCUMENTATION';
			status?: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
		} = {},
	) {
		const searchParams = new URLSearchParams();
		if (params.page) searchParams.append('page', params.page.toString());
		if (params.limit) searchParams.append('limit', params.limit.toString());
		if (params.type) searchParams.append('type', params.type);
		if (params.status) searchParams.append('status', params.status);

		const queryString = searchParams.toString();
		const endpoint = `generations${queryString ? `?${queryString}` : ''}`;

		return await this._api.get(
			endpoint,
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}

	/**
	 * Delete a generation
	 */
	async deleteGeneration(generationId: string) {
		return await this._api.delete(
			`generations/${generationId}`,
			null,
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}

	/**
	 * Update generation metadata
	 */
	async updateGeneration(
		generationId: string,
		body: {
			name?: string;
			metadata?: Record<string, unknown>;
		},
	) {
		return await this._api.put(
			`generations/${generationId}`,
			JSON.stringify(body),
			CommonFunction.createHeaders({
				withToken: true,
			}),
		);
	}
}

export { GenerationAPI };
