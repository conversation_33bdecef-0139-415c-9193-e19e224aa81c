import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { AppTemplate } from '@/components/templates';
import { NextPageWithLayout } from '../_app';
import {
	<PERSON>po<PERSON>,
	<PERSON><PERSON>,
	Badge,
	Card,
	CardContent,
	Alert,
} from '@/components/atoms';
import { CreateGenerationDialog } from '@/components/organisms/CreateGenerationDialog';
import { useGenerations } from '@/hooks/useGeneration';
import { generationService } from '@/services/generationService';
import {
	PlusIcon,
	SparklesIcon,
	ClockIcon,
	FolderIcon,
	UserIcon,
	CodeBracketIcon,
	DocumentTextIcon,
} from '@heroicons/react/24/outline';

// Types based on API response
interface GenerationListItem {
	id: string;
	name: string;
	type: 'UI' | 'DOCUMENTATION';
	prompt: string;
	initialPrompt: string;
	status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
	result: string | null;
	currentResult: string | null;
	metadata: {
		model?: string;
		processingTime?: number;
		tokensGenerated?: number;
		framework?: string;
		styling?: string;
		language?: string;
		error?: boolean;
		errorMessage?: string;
	} | null;
	createdAt: string;
	updatedAt: string;
	project: {
		id: string;
		name: string;
		description?: string;
		status: 'ACTIVE' | 'INACTIVE' | 'ARCHIVED';
	};
	createdBy: {
		id: string;
		email: string;
		fullName?: string;
	};
	promptHistory?: {
		id: string;
		prompt: string;
		description?: string;
		tags: string[];
		usageCount: number;
	};
}

const GenerationsIndexPage: NextPageWithLayout = () => {
	const router = useRouter();
	const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
	const { generations, isLoading, error, fetchGenerations } = useGenerations();

	useEffect(() => {
		fetchGenerations();
	}, [fetchGenerations]);

	const getStatusBadge = (status: GenerationListItem['status']) => {
		return generationService.getStatusBadge(status);
	};

	const handleViewGeneration = (id: string) => {
		router.push(`/generations/${id}`);
	};

	const handleCreateNew = () => {
		setIsCreateDialogOpen(true);
	};

	const handleCreateSuccess = (generationId: string) => {
		router.push(`/generations/${generationId}`);
	};

	if (isLoading) {
		return (
			<div className='flex items-center justify-center min-h-screen bg-layout-50'>
				<div className='text-center'>
					<div className='animate-spin rounded-full h-12 w-12 border-2 border-border-secondary border-t-primary mx-auto mb-4'></div>
					<Typography
						variant='body'
						color='secondary'>
						Loading generations...
					</Typography>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className='p-6 max-w-2xl mx-auto'>
				<Alert
					variant='negative'
					className='mb-4'>
					{error}
				</Alert>
			</div>
		);
	}

	return (
		<div className='min-h-screen bg-layout-50'>
			<div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8'>
				{/* Header */}
				<div className='flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-8'>
					<div className='flex-1'>
						<Typography
							variant='h1'
							weight='bold'
							className='mb-2'>
							Generations
						</Typography>
						<Typography
							variant='body-lg'
							color='secondary'>
							View and manage your AI-generated content
						</Typography>
					</div>
					<div className='flex-shrink-0'>
						<Button
							variant='primary'
							size='md'
							onClick={handleCreateNew}
							className='flex items-center gap-2 w-full sm:w-auto justify-center'>
							<PlusIcon className='w-5 h-5' />
							New Generation
						</Button>
					</div>
				</div>

				{/* Generations Grid */}
				{generations.length > 0 ? (
					<div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6'>
						{generations.map((generation) => {
							const statusBadge = getStatusBadge(generation.status);
							return (
								<Card
									key={generation.id}
									variant='default'
									size='medium'
									className='group hover:shadow-xl hover:shadow-primary/5 hover:-translate-y-1 transition-all duration-300 cursor-pointer border-border-secondary hover:border-primary/20 bg-surface-50 hover:bg-surface-100'
									onClick={() => handleViewGeneration(generation.id)}>
									<CardContent className='p-5 sm:p-6'>
										{/* Header */}
										<div className='flex items-start justify-between mb-4'>
											<div className='flex items-center gap-3'>
												<div
													className={`p-2 rounded-lg ${
														generation.type === 'UI'
															? 'bg-primary/10'
															: 'bg-accent-purple-100'
													}`}>
													{generation.type === 'UI' ? (
														<CodeBracketIcon className='w-4 h-4 text-primary' />
													) : (
														<DocumentTextIcon className='w-4 h-4 text-accent-purple-600' />
													)}
												</div>
												<div>
													<Typography
														variant='body-sm'
														weight='semibold'
														color='primary'
														className='uppercase tracking-wide'>
														{generation.type}
													</Typography>
													<Typography
														variant='caption'
														color='tertiary'>
														Generation
													</Typography>
												</div>
											</div>
											<Badge
												variant={statusBadge.variant}
												size='small'
												emphasis='light'
												className='flex-shrink-0'>
												{statusBadge.text}
											</Badge>
										</div>

										{/* Name */}
										<Typography
											variant='h4'
											weight='semibold'
											className='mb-2 line-clamp-1 group-hover:text-primary transition-colors duration-200'>
											{generation.name || 'Untitled Generation'}
										</Typography>

										{/* Prompt */}
										<Typography
											variant='body-sm'
											color='secondary'
											className='mb-4 line-clamp-3 leading-relaxed'>
											{generation.prompt}
										</Typography>

										{/* Tags */}
										{generation.promptHistory &&
											generation.promptHistory.tags.length > 0 && (
												<div className='flex flex-wrap gap-1.5 mb-4'>
													{generation.promptHistory.tags
														.slice(0, 2)
														.map((tag) => (
															<Badge
																key={tag}
																variant='secondary'
																size='small'
																emphasis='light'
																className='text-xs px-2 py-1'>
																{tag}
															</Badge>
														))}
													{generation.promptHistory.tags.length > 2 && (
														<Badge
															variant='secondary'
															size='small'
															emphasis='light'
															className='text-xs px-2 py-1'>
															+{generation.promptHistory.tags.length - 2}
														</Badge>
													)}
												</div>
											)}

										{/* Footer */}
										<div className='pt-4 border-t border-border-secondary/50'>
											<div className='flex items-center justify-between'>
												<div className='flex items-center gap-4 min-w-0 flex-1'>
													<div className='flex items-center gap-1.5 min-w-0'>
														<FolderIcon className='w-3.5 h-3.5 text-muted flex-shrink-0' />
														<Typography
															variant='caption'
															color='secondary'
															className='truncate'>
															{generation.project.name}
														</Typography>
													</div>
													<div className='flex items-center gap-1.5 min-w-0'>
														<UserIcon className='w-3.5 h-3.5 text-muted flex-shrink-0' />
														<Typography
															variant='caption'
															color='secondary'
															className='truncate'>
															{generation.createdBy.fullName?.split(' ')[0] ||
																generation.createdBy.email.split('@')[0]}
														</Typography>
													</div>
												</div>
												<div className='flex items-center gap-1.5 flex-shrink-0 ml-2'>
													<ClockIcon className='w-3.5 h-3.5 text-muted' />
													<Typography
														variant='caption'
														color='tertiary'>
														{new Date(generation.createdAt).toLocaleDateString(
															'en-US',
															{
																month: 'short',
																day: 'numeric',
															},
														)}
													</Typography>
												</div>
											</div>
										</div>
									</CardContent>
								</Card>
							);
						})}
					</div>
				) : (
					<div className='text-center py-20 px-4'>
						<div className='max-w-md mx-auto'>
							{/* Icon */}
							<div className='relative mb-8'>
								<div className='w-20 h-20 bg-gradient-to-br from-primary/10 to-primary/5 rounded-3xl flex items-center justify-center mx-auto mb-4 shadow-lg shadow-primary/5'>
									<SparklesIcon className='w-10 h-10 text-primary' />
								</div>
								<div className='absolute -top-1 -right-1 w-6 h-6 bg-accent-purple-100 rounded-full flex items-center justify-center'>
									<PlusIcon className='w-3 h-3 text-accent-purple-600' />
								</div>
							</div>

							{/* Content */}
							<Typography
								variant='h2'
								weight='bold'
								className='mb-3'>
								Start Creating
							</Typography>
							<Typography
								variant='body-lg'
								color='secondary'
								className='mb-2'>
								No generations yet
							</Typography>
							<Typography
								variant='body'
								color='tertiary'
								className='mb-8 leading-relaxed'>
								Transform your ideas into reality with AI-powered generation.
								Create UI components, documentation, and more with simple
								prompts.
							</Typography>

							{/* Action */}
							<Button
								variant='primary'
								size='lg'
								onClick={handleCreateNew}
								className='flex items-center gap-2 mx-auto px-8 py-3 shadow-lg shadow-primary/20 hover:shadow-xl hover:shadow-primary/25 transition-all duration-300'>
								<PlusIcon className='w-5 h-5' />
								Create Your First Generation
							</Button>

							{/* Helper text */}
							<Typography
								variant='caption'
								color='tertiary'
								className='mt-4 flex items-center justify-center gap-1'>
								<span>💡</span>
								Tip: Describe what you want to build in detail for better
								results
							</Typography>
						</div>
					</div>
				)}
			</div>

			{/* Create Generation Dialog */}
			<CreateGenerationDialog
				isOpen={isCreateDialogOpen}
				onClose={() => setIsCreateDialogOpen(false)}
				onSuccess={handleCreateSuccess}
			/>
		</div>
	);
};

GenerationsIndexPage.getLayout = (page) => {
	return <AppTemplate>{page}</AppTemplate>;
};

export default GenerationsIndexPage;
