import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { AppTemplate } from '@/components/templates';
import { NextPageWithLayout } from '../_app';
import { GenerationsLayout } from '@/components/templates/generations/GenerationsLayout';
import { ChatPanel } from '@/components/templates/generations/ChatPanel';
import { CodePreviewPanel } from '@/components/templates/generations/CodePreviewPanel';
import { Typography, Alert, Badge } from '@/components/atoms';
import {
	useGeneration,
	useConversation,
	useGenerationStream,
} from '@/hooks/useGeneration';
import { generationService } from '@/services/generationService';
import { formatDistanceToNow } from 'date-fns';
import {
	ArrowLeftIcon,
	FolderIcon,
	UserIcon,
	ClockIcon,
	CodeBracketIcon,
	DocumentTextIcon,
} from '@heroicons/react/24/outline';

// Use the same interface as the API
interface GenerationData {
	id: string;
	name: string;
	type: 'UI' | 'DOCUMENTATION';
	prompt: string;
	initialPrompt: string;
	status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
	result: string | null;
	currentResult: string | null;
	metadata: {
		model?: string;
		processingTime?: number;
		tokensGenerated?: number;
		framework?: string;
		styling?: string;
		language?: string;
		error?: boolean;
		errorMessage?: string;
	} | null;
	createdAt: string;
	updatedAt: string;
	projectId: string;
	createdById: string;
	promptHistoryId?: string;
	// Relations
	project?: {
		id: string;
		name: string;
		description?: string;
		status?: 'ACTIVE' | 'INACTIVE' | 'ARCHIVED';
	};
	createdBy?: {
		id: string;
		email: string;
		fullName?: string;
	};
	promptHistory?: {
		id: string;
		prompt: string;
		description?: string;
		tags: string[];
		usageCount: number;
	};
}

const GenerationPage: NextPageWithLayout = () => {
	const router = useRouter();
	const { id } = router.query;
	const [pollInterval, setPollInterval] = useState<NodeJS.Timeout | null>(null);
	const [isGenerating, setIsGenerating] = useState(false);

	// Use the real API hooks
	const { generation, isLoading, error, fetchGeneration } = useGeneration(
		id as string,
	);
	const { messages, addMessage } = useConversation(id as string);

	// Use streaming for real-time updates
	const { isConnected, lastUpdate } = useGenerationStream(id as string);

	// Handle streaming updates
	useEffect(() => {
		if (lastUpdate) {
			console.log('Received stream update:', lastUpdate);
			// Refresh generation data when we receive updates
			fetchGeneration();
		}
	}, [lastUpdate, fetchGeneration]);

	// Fallback polling for older browsers or when streaming fails
	useEffect(() => {
		if (
			generation &&
			generationService.isProcessing(generation.status) &&
			!isConnected
		) {
			const interval = generationService.pollForUpdates(
				generation.id,
				(updatedGeneration) => {
					console.log('Generation updated via polling:', updatedGeneration);
				},
			);
			setPollInterval(interval);
		}

		return () => {
			if (pollInterval) {
				clearInterval(pollInterval);
			}
		};
	}, [generation, pollInterval, isConnected]);

	// Handle sending new messages
	const handleSendMessage = async (message: string) => {
		if (!generation) return;

		setIsGenerating(true);
		try {
			await addMessage({
				content: message,
				inputData: { timestamp: Date.now() },
			});
		} finally {
			setIsGenerating(false);
		}
	};

	const handleRegenerateCode = async (prompt: string) => {
		if (!generation) return;

		setIsGenerating(true);
		try {
			await addMessage({
				content: prompt,
				inputData: { regenerate: true },
			});
		} finally {
			setIsGenerating(false);
		}
	};

	const handleBack = () => {
		router.back();
	};

	if (isLoading) {
		return (
			<div className='flex items-center justify-center min-h-screen bg-layout-50'>
				<div className='text-center'>
					<div className='animate-spin rounded-full h-12 w-12 border-2 border-border-secondary border-t-primary mx-auto mb-4'></div>
					<Typography
						variant='body'
						color='secondary'>
						Loading generation...
					</Typography>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className='p-6 max-w-2xl mx-auto'>
				<Alert
					variant='negative'
					className='mb-4'>
					{error}
				</Alert>
			</div>
		);
	}

	if (!generation) {
		return (
			<div className='flex items-center justify-center min-h-screen bg-layout-50'>
				<div className='text-center'>
					<Typography
						variant='h3'
						color='secondary'
						className='mb-4'>
						Generation not found
					</Typography>
					<Typography
						variant='body'
						color='tertiary'>
						The generation you&apos;re looking for doesn&apos;t exist or has
						been removed.
					</Typography>
				</div>
			</div>
		);
	}

	const getStatusBadge = (status: GenerationData['status']) => {
		switch (status) {
			case 'COMPLETED':
				return { variant: 'success' as const, text: 'Completed' };
			case 'IN_PROGRESS':
				return { variant: 'warning' as const, text: 'In Progress' };
			case 'PENDING':
				return { variant: 'secondary' as const, text: 'Pending' };
			case 'FAILED':
				return { variant: 'error' as const, text: 'Failed' };
			default:
				return { variant: 'secondary' as const, text: 'Unknown' };
		}
	};

	const statusBadge = getStatusBadge(generation.status);

	return (
		<div className='h-screen flex flex-col bg-layout-50'>
			{/* Header */}
			<header className='flex-shrink-0 bg-surface-50 border-b border-border-secondary'>
				<div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6'>
					<div className='flex flex-col sm:flex-row sm:items-center justify-between gap-4'>
						{/* Back Button & Info */}
						<div className='flex items-start gap-4'>
							<button
								onClick={handleBack}
								className='flex-shrink-0 p-2 rounded-lg hover:bg-surface-100 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors duration-200'
								aria-label='Go back'>
								<ArrowLeftIcon className='w-5 h-5 text-muted' />
							</button>

							<div className='min-w-0 flex-1'>
								{/* Type and Status */}
								<div className='flex items-center gap-3 mb-2'>
									<div
										className={`p-2 rounded-lg ${
											generation.type === 'UI'
												? 'bg-primary/10'
												: 'bg-accent-purple-100'
										}`}>
										{generation.type === 'UI' ? (
											<CodeBracketIcon className='w-4 h-4 text-primary' />
										) : (
											<DocumentTextIcon className='w-4 h-4 text-accent-purple-600' />
										)}
									</div>
									<div className='flex items-center gap-2'>
										<Typography
											variant='body-sm'
											weight='semibold'
											color='primary'
											className='uppercase tracking-wide'>
											{generation.type}
										</Typography>
										<Badge
											variant={statusBadge.variant}
											size='small'
											emphasis='light'>
											{statusBadge.text}
										</Badge>
									</div>
								</div>

								{/* Name */}
								<Typography
									variant='h3'
									weight='bold'
									className='mb-3 line-clamp-2'>
									{generation.name || 'Untitled Generation'}
								</Typography>

								{/* Meta Information */}
								<div className='flex flex-wrap items-center gap-4 text-sm'>
									<div className='flex items-center gap-1.5'>
										<FolderIcon className='w-4 h-4 text-muted' />
										<Typography
											variant='body-sm'
											color='secondary'>
											{generation.project?.name || 'Unknown Project'}
										</Typography>
									</div>
									<div className='flex items-center gap-1.5'>
										<UserIcon className='w-4 h-4 text-muted' />
										<Typography
											variant='body-sm'
											color='secondary'>
											{generation.createdBy?.fullName ||
												generation.createdBy?.email ||
												'Unknown User'}
										</Typography>
									</div>
									<div className='flex items-center gap-1.5'>
										<ClockIcon className='w-4 h-4 text-muted' />
										<Typography
											variant='body-sm'
											color='tertiary'>
											{formatDistanceToNow(new Date(generation.createdAt), {
												addSuffix: true,
											})}
										</Typography>
									</div>
								</div>
							</div>
						</div>

						{/* Header Actions */}
						<div className='flex items-center gap-2 flex-shrink-0'>
							{generation.promptHistory &&
								generation.promptHistory.tags.length > 0 && (
									<div className='flex flex-wrap items-center gap-1.5'>
										{generation.promptHistory.tags.slice(0, 3).map((tag) => (
											<Badge
												key={tag}
												variant='secondary'
												size='small'
												emphasis='light'
												className='text-xs'>
												{tag}
											</Badge>
										))}
										{generation.promptHistory.tags.length > 3 && (
											<Badge
												variant='secondary'
												size='small'
												emphasis='light'
												className='text-xs'>
												+{generation.promptHistory.tags.length - 3}
											</Badge>
										)}
									</div>
								)}
						</div>
					</div>
				</div>
			</header>

			{/* Main Content */}
			<div className='flex-1 flex overflow-hidden'>
				<GenerationsLayout
					chatPanel={
						<ChatPanel
							generation={generation}
							messages={messages}
							onSendMessage={handleSendMessage}
							onRegenerateCode={handleRegenerateCode}
							isGenerating={isGenerating}
						/>
					}
					codePreviewPanel={
						<CodePreviewPanel
							generation={generation}
							onCodeUpdate={(code) => {
								// Handle code updates
								console.log('Code updated:', code);
							}}
						/>
					}
				/>
			</div>
		</div>
	);
};

GenerationPage.getLayout = (page) => {
	return <AppTemplate>{page}</AppTemplate>;
};

export default GenerationPage;
