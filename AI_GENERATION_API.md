# 🤖 AI-Assisted Generation API Documentation

## Overview

The AI Generation system provides conversational AI capabilities for generating UI components and documentation within projects. It uses a chat completion-based approach with conversation memory and project context.

## Base URL
```
http://localhost:3000
```

## Authentication
All endpoints require JWT Bearer token authentication:
```
Authorization: Bearer <your_jwt_token>
```

---

## 📋 Available Endpoints

### 1. **Create Conversational Generation**
```http
POST /generations/conversational
```

**Description:** Creates a new conversational generation with memory support

**Request Body:**
```typescript
{
  name: string,                    // User-friendly name (1-100 chars)
  type: "UI" | "DOCUMENTATION",   // Generation type
  initialPrompt: string,          // Initial prompt (10-2000 chars)
  projectId: string,              // UUID of the project
  promptHistoryId?: string        // Optional: Link to existing prompt
}
```

**Example Request:**
```json
{
  "name": "Dashboard Component",
  "type": "UI",
  "initialPrompt": "Create a responsive dashboard component with charts and metrics",
  "projectId": "123e4567-e89b-12d3-a456-************"
}
```

**Response:**
```typescript
{
  message: string,
  data: {
    id: string,                   // Generation UUID
    name: string,
    type: "UI" | "DOCUMENTATION",
    prompt: string,
    initialPrompt: string,
    status: "PENDING" | "IN_PROGRESS" | "COMPLETED" | "FAILED",
    result: string | null,
    currentResult: string | null,
    metadata: object | null,
    projectId: string,
    createdById: string,
    createdAt: string,
    updatedAt: string
  },
  statusCode: 201
}
```

---

### 2. **Add Message to Conversation**
```http
POST /generations/{id}/messages
```

**Description:** Adds a new message to an existing conversational generation

**Path Parameters:**
- `id` (string, UUID): Generation ID

**Request Body:**
```typescript
{
  content: string,        // Message content (1-2000 chars)
  inputData?: any        // Optional: Additional data (code, files, preferences)
}
```

**Example Request:**
```json
{
  "content": "Make the component darker and add hover effects",
  "inputData": {
    "codeSnippet": "const example = 'code';",
    "preferences": { "theme": "dark" }
  }
}
```

**Response:**
```typescript
{
  message: string,
  data: {
    id: string,                   // Message UUID
    generationId: string,
    role: "USER" | "ASSISTANT" | "SYSTEM",
    content: string,
    inputData: any | null,
    outputData: any | null,
    messageIndex: number,
    status: "SENT" | "DELIVERED" | "FAILED",
    createdAt: string,
    updatedAt: string
  },
  statusCode: 201
}
```

---

### 3. **Get Conversation History**
```http
GET /generations/{id}/conversation
```

**Description:** Retrieves the conversation history for a generation

**Path Parameters:**
- `id` (string, UUID): Generation ID

**Query Parameters:**
- `page` (number, optional): Page number (default: 1, min: 1)
- `limit` (number, optional): Messages per page (default: 50, max: 100)

**Example Request:**
```
GET /generations/123e4567-e89b-12d3-a456-************/conversation?page=1&limit=20
```

**Response:**
```typescript
{
  message: string,
  data: {
    messages: [
      {
        id: string,
        generationId: string,
        role: "USER" | "ASSISTANT" | "SYSTEM",
        content: string,
        inputData: any | null,
        outputData: any | null,
        messageIndex: number,
        status: "SENT" | "DELIVERED" | "FAILED",
        createdAt: string,
        updatedAt: string,
        createdBy?: {
          id: string,
          email: string,
          name: string
        }
      }
    ],
    pagination: {
      page: number,
      limit: number,
      total: number,
      totalPages: number
    }
  },
  statusCode: 200
}
```

---

### 4. **Get Current Generation Result**
```http
GET /generations/{id}/result
```

**Description:** Retrieves the current result of a generation

**Path Parameters:**
- `id` (string, UUID): Generation ID

**Response:**
```typescript
{
  message: string,
  data: {
    id: string,
    name: string,
    type: "UI" | "DOCUMENTATION",
    status: "PENDING" | "IN_PROGRESS" | "COMPLETED" | "FAILED",
    result: string | null,        // Final result
    currentResult: string | null, // Current/latest result
    metadata: {
      model?: string,
      processingTime?: number,
      tokensGenerated?: number,
      framework?: string,
      styling?: string,
      language?: string,
      error?: boolean,
      errorMessage?: string
    } | null,
    createdAt: string,
    updatedAt: string
  },
  statusCode: 200
}
```

---

## 🏗️ Project-Based Endpoints

### 5. **Create Conversational Generation for Project**
```http
POST /projects/{projectId}/generations/conversational
```

**Description:** Creates a conversational generation within a specific project

**Path Parameters:**
- `projectId` (string, UUID): Project ID

**Request Body:** Same as `/generations/conversational` but without `projectId`
```typescript
{
  name: string,
  type: "UI" | "DOCUMENTATION",
  initialPrompt: string,
  promptHistoryId?: string
}
```

---

### 6. **Create Legacy Generation for Project**
```http
POST /projects/{projectId}/generations
```

**Description:** Creates a legacy generation (backward compatibility)

**Path Parameters:**
- `projectId` (string, UUID): Project ID

**Request Body:**
```typescript
{
  type: "UI" | "DOCUMENTATION",
  prompt: string,              // Generation prompt (10-2000 chars)
  promptHistoryId?: string     // Optional: Link to existing prompt
}
```

---

## 🔄 User Flow & Relationships

### **Conversational Generation Flow**

```mermaid
graph TD
    A[User creates conversational generation] --> B[POST /generations/conversational]
    B --> C[System creates generation with PENDING status]
    C --> D[System processes initial prompt with AI]
    D --> E[Generation status becomes IN_PROGRESS]
    E --> F[AI generates response using chat completion]
    F --> G[System stores result and updates status to COMPLETED]
    G --> H[User can get result via GET /generations/{id}/result]
    
    H --> I[User adds follow-up message]
    I --> J[POST /generations/{id}/messages]
    J --> K[System processes message with conversation context]
    K --> L[AI generates contextual response]
    L --> M[System updates currentResult]
    M --> N[User can view conversation history]
    N --> O[GET /generations/{id}/conversation]
    
    O --> I
```

### **Data Relationships**

```
Project (1) ──── (N) Generation
    │                   │
    │                   ├── name: string
    │                   ├── type: UI | DOCUMENTATION  
    │                   ├── status: PENDING | IN_PROGRESS | COMPLETED | FAILED
    │                   ├── result: string (final result)
    │                   ├── currentResult: string (latest result)
    │                   └── metadata: JSON
    │
    └── User (1) ──── (N) Generation (createdBy)
                           │
                           └── (1) ──── (N) ConversationMessage
                                           ├── role: USER | ASSISTANT | SYSTEM
                                           ├── content: string
                                           ├── inputData: JSON
                                           ├── outputData: JSON
                                           ├── messageIndex: number
                                           └── status: SENT | DELIVERED | FAILED
```

---

## 💡 Frontend Integration Examples

### **1. Create New UI Generation**
```javascript
const createUIGeneration = async (projectId, prompt) => {
  const response = await fetch('/generations/conversational', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      name: 'New UI Component',
      type: 'UI',
      initialPrompt: prompt,
      projectId: projectId
    })
  });
  
  const result = await response.json();
  return result.data; // Returns generation object
};
```

### **2. Continue Conversation**
```javascript
const addMessage = async (generationId, message) => {
  const response = await fetch(`/generations/${generationId}/messages`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      content: message,
      inputData: { timestamp: Date.now() }
    })
  });
  
  return await response.json();
};
```

### **3. Get Latest Result**
```javascript
const getCurrentResult = async (generationId) => {
  const response = await fetch(`/generations/${generationId}/result`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  const result = await response.json();
  return result.data.currentResult; // Returns generated code/documentation
};
```

### **4. Load Conversation History**
```javascript
const getConversation = async (generationId, page = 1) => {
  const response = await fetch(
    `/generations/${generationId}/conversation?page=${page}&limit=20`,
    {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    }
  );
  
  const result = await response.json();
  return result.data.messages; // Returns message array
};
```

---

## 🎯 Generation Types

### **UI Generation**
- **Purpose:** Generate React TypeScript components with Tailwind CSS
- **System Prompt:** "Expert React TypeScript developer"
- **Output:** React component code with proper interfaces, responsive design, and accessibility
- **Model:** `meta-llama/CodeLlama-7b-Instruct-hf`

### **Documentation Generation**  
- **Purpose:** Generate technical documentation in markdown format
- **System Prompt:** "Technical documentation expert"
- **Output:** Comprehensive documentation with examples and best practices
- **Model:** `meta-llama/Llama-3.2-3B-Instruct`

---

## ⚡ Key Features

- ✅ **Conversational Memory:** Maintains context across messages
- ✅ **Project Context:** Includes project information in AI prompts
- ✅ **Real-time Processing:** Asynchronous AI generation
- ✅ **Pagination:** Efficient conversation history loading
- ✅ **Role-based Access:** User and admin permissions
- ✅ **Error Handling:** Comprehensive error responses
- ✅ **Metadata Tracking:** Model info, processing time, token usage

---

## 🚨 Error Responses

All endpoints return consistent error responses:

```typescript
{
  message: string,
  error: string,
  statusCode: number
}
```

**Common Status Codes:**
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (missing/invalid token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found (generation/project not found)
- `500` - Internal Server Error

---

## 🔧 Environment Configuration

Required environment variables:
```bash
# Hugging Face Configuration
HUGGINGFACE_API_KEY=your_api_key
HUGGINGFACE_DEFAULT_MODEL=meta-llama/Llama-3.2-3B-Instruct
HUGGINGFACE_UI_MODEL=meta-llama/CodeLlama-7b-Instruct-hf
HUGGINGFACE_DOCS_MODEL=meta-llama/Llama-3.2-3B-Instruct
HUGGINGFACE_DEFAULT_PROVIDER=hf-inference
HUGGINGFACE_MAX_TOKENS=1024
HUGGINGFACE_TEMPERATURE=0.7

# JWT Configuration
JWT_SECRET=your_jwt_secret
```

This API provides a complete conversational AI system for generating UI components and documentation with full conversation memory and project context! 🚀
